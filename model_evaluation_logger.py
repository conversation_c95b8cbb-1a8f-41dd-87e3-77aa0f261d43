"""
Model Evaluation Logging System

This module provides comprehensive logging functionality for machine learning model evaluation results.
Supports both Excel and Google Sheets integration with structured data format including experiment 
metadata, model metrics, and timestamps.

Features:
- Excel (.xlsx) file logging with automatic file creation and appending
- Google Sheets integration (when credentials are available)
- Comprehensive error handling and fallback mechanisms
- Structured data format with experiment metadata
- Support for multiple metrics: AUC-ROC, Precision, Recall, F1-Score
- Automatic timestamp generation
- Data integrity validation

Author: Augment Agent
Date: 2025-06-24
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import json
from typing import Dict, List, Optional, Union, Any
import warnings

# Optional imports for Google Sheets integration
try:
    import gspread
    from google.oauth2.service_account import Credentials
    GOOGLE_SHEETS_AVAILABLE = True
except ImportError:
    GOOGLE_SHEETS_AVAILABLE = False
    warnings.warn("Google Sheets integration not available. Install gspread and google-auth for full functionality.")

# Excel support
try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    warnings.warn("Excel support not available. Install openpyxl for Excel functionality.")


class ModelEvaluationLogger:
    """
    Comprehensive logging system for machine learning model evaluation results.
    
    Supports multiple output formats and provides robust error handling.
    """
    
    def __init__(self, 
                 excel_file_path: str = "model_evaluation_results.xlsx",
                 google_sheets_name: Optional[str] = None,
                 google_credentials_path: Optional[str] = None):
        """
        Initialize the Model Evaluation Logger.
        
        Parameters:
        -----------
        excel_file_path : str
            Path to the Excel file for logging results
        google_sheets_name : str, optional
            Name of the Google Sheets document
        google_credentials_path : str, optional
            Path to Google Sheets service account credentials JSON file
        """
        self.excel_file_path = excel_file_path
        self.google_sheets_name = google_sheets_name
        self.google_credentials_path = google_credentials_path
        self.google_client = None
        
        # Initialize Google Sheets client if credentials are provided
        if GOOGLE_SHEETS_AVAILABLE and google_credentials_path and google_sheets_name:
            self._initialize_google_sheets()
        
        # Define the standard column structure
        self.columns = [
            'Timestamp',
            'Experiment_Name', 
            'Model_Type',
            'AUC_ROC',
            'Precision',
            'Recall',
            'F1_Score',
            'Preprocessing_Steps',
            'Class_Balance_Method',
            'Random_State',
            'Dataset_Size_Train',
            'Dataset_Size_Test',
            'Notes'
        ]
        
        # Initialize Excel file if it doesn't exist
        if EXCEL_AVAILABLE:
            self._initialize_excel_file()
    
    def _initialize_google_sheets(self):
        """Initialize Google Sheets client with service account credentials."""
        try:
            if not os.path.exists(self.google_credentials_path):
                print(f"⚠️  Google Sheets credentials file not found: {self.google_credentials_path}")
                return
            
            scope = ['https://spreadsheets.google.com/feeds',
                    'https://www.googleapis.com/auth/drive']
            
            credentials = Credentials.from_service_account_file(
                self.google_credentials_path, scopes=scope)
            self.google_client = gspread.authorize(credentials)
            print(f"✅ Google Sheets client initialized successfully")
            
        except Exception as e:
            print(f"❌ Failed to initialize Google Sheets client: {str(e)}")
            self.google_client = None
    
    def _initialize_excel_file(self):
        """Initialize Excel file with headers if it doesn't exist."""
        try:
            if not os.path.exists(self.excel_file_path):
                # Create new Excel file with headers
                df = pd.DataFrame(columns=self.columns)
                df.to_excel(self.excel_file_path, index=False, engine='openpyxl')
                print(f"✅ Created new Excel file: {self.excel_file_path}")
            else:
                print(f"✅ Excel file already exists: {self.excel_file_path}")
                
        except Exception as e:
            print(f"❌ Failed to initialize Excel file: {str(e)}")
    
    def log_model_results(self,
                         experiment_name: str,
                         model_results: Dict[str, Dict[str, Any]],
                         preprocessing_steps: List[str] = None,
                         class_balance_method: str = "None",
                         dataset_info: Dict[str, int] = None,
                         notes: str = "") -> bool:
        """
        Log model evaluation results to configured output formats.
        
        Parameters:
        -----------
        experiment_name : str
            Name of the experiment (e.g., "Baseline_Models", "SMOTE_Balanced")
        model_results : dict
            Dictionary containing model results with structure:
            {
                'model_name': {
                    'auc_roc': float,
                    'precision': float,
                    'recall': float (optional),
                    'f1_score': float (optional)
                }
            }
        preprocessing_steps : list, optional
            List of preprocessing steps applied
        class_balance_method : str
            Method used for class balancing (e.g., "SMOTE", "None")
        dataset_info : dict, optional
            Dictionary with 'train_size' and 'test_size' keys
        notes : str
            Additional notes about the experiment
            
        Returns:
        --------
        bool
            True if logging was successful, False otherwise
        """
        try:
            # Prepare data for logging
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            preprocessing_str = ", ".join(preprocessing_steps) if preprocessing_steps else "None"
            
            # Default dataset info if not provided
            if dataset_info is None:
                dataset_info = {'train_size': 'Unknown', 'test_size': 'Unknown'}
            
            # Create records for each model
            records = []
            for model_name, metrics in model_results.items():
                record = {
                    'Timestamp': timestamp,
                    'Experiment_Name': experiment_name,
                    'Model_Type': model_name,
                    'AUC_ROC': metrics.get('auc_roc', np.nan),
                    'Precision': metrics.get('precision', np.nan),
                    'Recall': metrics.get('recall', np.nan),
                    'F1_Score': metrics.get('f1_score', np.nan),
                    'Preprocessing_Steps': preprocessing_str,
                    'Class_Balance_Method': class_balance_method,
                    'Random_State': 42,  # Default from the scripts
                    'Dataset_Size_Train': dataset_info.get('train_size', 'Unknown'),
                    'Dataset_Size_Test': dataset_info.get('test_size', 'Unknown'),
                    'Notes': notes
                }
                records.append(record)
            
            # Log to Excel
            excel_success = self._log_to_excel(records)
            
            # Log to Google Sheets (if available)
            google_success = self._log_to_google_sheets(records)
            
            # Report results
            if excel_success or google_success:
                print(f"✅ Successfully logged {len(records)} model results for experiment: {experiment_name}")
                if excel_success:
                    print(f"   📊 Excel: {self.excel_file_path}")
                if google_success:
                    print(f"   📊 Google Sheets: {self.google_sheets_name}")
                return True
            else:
                print(f"❌ Failed to log results for experiment: {experiment_name}")
                return False
                
        except Exception as e:
            print(f"❌ Error logging model results: {str(e)}")
            return False
    
    def _log_to_excel(self, records: List[Dict]) -> bool:
        """Log records to Excel file."""
        if not EXCEL_AVAILABLE:
            print("⚠️  Excel logging skipped - openpyxl not available")
            return False
        
        try:
            # Read existing data
            if os.path.exists(self.excel_file_path):
                existing_df = pd.read_excel(self.excel_file_path, engine='openpyxl')
            else:
                existing_df = pd.DataFrame(columns=self.columns)
            
            # Append new records
            new_df = pd.DataFrame(records)
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            
            # Save to Excel
            combined_df.to_excel(self.excel_file_path, index=False, engine='openpyxl')
            return True
            
        except Exception as e:
            print(f"❌ Excel logging failed: {str(e)}")
            return False
    
    def _log_to_google_sheets(self, records: List[Dict]) -> bool:
        """Log records to Google Sheets."""
        if not GOOGLE_SHEETS_AVAILABLE or not self.google_client:
            return False
        
        try:
            # Open or create the spreadsheet
            try:
                spreadsheet = self.google_client.open(self.google_sheets_name)
            except gspread.SpreadsheetNotFound:
                spreadsheet = self.google_client.create(self.google_sheets_name)
                print(f"✅ Created new Google Sheets document: {self.google_sheets_name}")
            
            # Get or create the worksheet
            try:
                worksheet = spreadsheet.sheet1
            except:
                worksheet = spreadsheet.add_worksheet(title="Model Results", rows="1000", cols="20")
            
            # Check if headers exist
            if not worksheet.get_all_values():
                worksheet.append_row(self.columns)
            
            # Append records
            for record in records:
                row = [record.get(col, '') for col in self.columns]
                worksheet.append_row(row)
            
            return True
            
        except Exception as e:
            print(f"❌ Google Sheets logging failed: {str(e)}")
            return False
    
    def get_logged_results(self, experiment_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve logged results from Excel file.
        
        Parameters:
        -----------
        experiment_name : str, optional
            Filter results by experiment name
            
        Returns:
        --------
        pd.DataFrame
            DataFrame containing logged results
        """
        try:
            if not os.path.exists(self.excel_file_path):
                print(f"⚠️  No results file found: {self.excel_file_path}")
                return pd.DataFrame()
            
            df = pd.read_excel(self.excel_file_path, engine='openpyxl')
            
            if experiment_name:
                df = df[df['Experiment_Name'] == experiment_name]
            
            return df
            
        except Exception as e:
            print(f"❌ Error retrieving logged results: {str(e)}")
            return pd.DataFrame()
    
    def generate_summary_report(self) -> str:
        """
        Generate a comprehensive summary report of all logged results.
        
        Returns:
        --------
        str
            Formatted summary report
        """
        try:
            df = self.get_logged_results()
            
            if df.empty:
                return "No logged results found."
            
            report = []
            report.append("=" * 80)
            report.append("MODEL EVALUATION RESULTS SUMMARY")
            report.append("=" * 80)
            report.append(f"Total Experiments: {df['Experiment_Name'].nunique()}")
            report.append(f"Total Model Evaluations: {len(df)}")
            report.append(f"Date Range: {df['Timestamp'].min()} to {df['Timestamp'].max()}")
            report.append("")
            
            # Summary by experiment
            for exp_name in df['Experiment_Name'].unique():
                exp_df = df[df['Experiment_Name'] == exp_name]
                report.append(f"📊 EXPERIMENT: {exp_name}")
                report.append("-" * 50)
                
                for _, row in exp_df.iterrows():
                    report.append(f"   {row['Model_Type']}:")
                    report.append(f"      AUC-ROC: {row['AUC_ROC']:.4f}")
                    report.append(f"      Precision: {row['Precision']:.4f}")
                    if not pd.isna(row['Recall']):
                        report.append(f"      Recall: {row['Recall']:.4f}")
                    if not pd.isna(row['F1_Score']):
                        report.append(f"      F1-Score: {row['F1_Score']:.4f}")
                    report.append("")
                
                report.append("")
            
            return "\n".join(report)
            
        except Exception as e:
            return f"Error generating summary report: {str(e)}"


# Convenience function for easy integration
def log_experiment_results(experiment_name: str,
                          model_results: Dict[str, Dict[str, Any]],
                          preprocessing_steps: List[str] = None,
                          class_balance_method: str = "None",
                          dataset_info: Dict[str, int] = None,
                          notes: str = "",
                          excel_file: str = "model_evaluation_results.xlsx") -> bool:
    """
    Convenience function to log experiment results with minimal setup.
    
    Parameters:
    -----------
    experiment_name : str
        Name of the experiment
    model_results : dict
        Dictionary containing model results
    preprocessing_steps : list, optional
        List of preprocessing steps applied
    class_balance_method : str
        Method used for class balancing
    dataset_info : dict, optional
        Dictionary with dataset size information
    notes : str
        Additional notes
    excel_file : str
        Path to Excel file for logging
        
    Returns:
    --------
    bool
        True if logging was successful
    """
    logger = ModelEvaluationLogger(excel_file_path=excel_file)
    return logger.log_model_results(
        experiment_name=experiment_name,
        model_results=model_results,
        preprocessing_steps=preprocessing_steps,
        class_balance_method=class_balance_method,
        dataset_info=dataset_info,
        notes=notes
    )
