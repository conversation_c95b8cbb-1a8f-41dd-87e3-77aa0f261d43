# SMOTE Class Balancing Experiment Summary

## Overview

This document summarizes the comprehensive SMOTE (Synthetic Minority Oversampling Technique) experiment conducted to address class imbalance in the Ocrevus discontinuation prediction dataset. The experiment compares original baseline models with SMOTE-balanced models across multiple performance metrics.

## Experiment Design

### Class Imbalance Problem
- **Original Distribution**: 78.4% Active (0), 21.6% Discontinued (1)
- **Target Distribution**: 50% Active (0), 50% Discontinued (1)
- **Imbalance Ratio**: 3.6:1 (Active:Discontinued)

### Processing Pipeline
1. **Data Split**: 70-30 stratified train-test split (preserves original class distribution)
2. **Feature Scaling**: StandardScaler fitted on training data only
3. **SMOTE Application**: Applied only to scaled training data
4. **Model Training**: Same three models trained on balanced data
5. **Evaluation**: Models evaluated on original (unmodified) test set
6. **SHAP Analysis**: Feature importance analysis on entire test dataset

### Technical Implementation
- **SMOTE Parameters**: `sampling_strategy='auto'`, `random_state=42`
- **Scaling**: StandardScaler with fit on training data only
- **Models**: Random Forest, Gradient Boosting, XGBoost (default parameters)
- **Evaluation Metrics**: AUC-ROC, Precision, Recall, F1-Score

## Results Summary

### Training Data Transformation
- **Original Training Samples**: 8,887
- **SMOTE-Balanced Training Samples**: 13,942 (+5,055 synthetic samples)
- **Class Distribution After SMOTE**: 6,971 Active (50%), 6,971 Discontinued (50%)

### Model Performance Comparison

| Model | Metric | Original | SMOTE | Difference | Improvement |
|-------|--------|----------|-------|------------|-------------|
| **Random Forest** | AUC-ROC | 0.9697 | 0.9652 | -0.0045 | -0.46% |
| | Precision | 0.9595 | 0.8889 | -0.0706 | -7.36% |
| | Recall | 0.8659 | 0.9549 | +0.0890 | +10.28% |
| | F1-Score | 0.9105 | 0.9211 | +0.0106 | +1.16% |
| **Gradient Boosting** | AUC-ROC | 0.9767 | 0.9729 | -0.0038 | -0.39% |
| | Precision | 0.9655 | 0.9024 | -0.0631 | -6.54% |
| | Recall | 0.8854 | 0.9549 | +0.0695 | +7.85% |
| | F1-Score | 0.9237 | 0.9279 | +0.0042 | +0.45% |
| **XGBoost** | AUC-ROC | 0.9818 | 0.9780 | -0.0038 | -0.39% |
| | Precision | 0.9530 | 0.9024 | -0.0506 | -5.31% |
| | Recall | 0.9000 | 0.9549 | +0.0549 | +6.10% |
| | F1-Score | 0.9258 | 0.9279 | +0.0021 | +0.23% |

### Key Findings

#### 1. **Recall Improvement**
- **Average Recall Improvement**: +7.41% across all models
- **Best Improvement**: Random Forest (+10.28%)
- **Clinical Impact**: Better identification of patients at risk of discontinuation

#### 2. **Precision Trade-off**
- **Average Precision Decrease**: -6.40% across all models
- **Trade-off**: More false positives in exchange for fewer false negatives
- **Clinical Impact**: More patients flagged for intervention (some unnecessarily)

#### 3. **F1-Score Balance**
- **Average F1-Score Improvement**: +0.56% across all models
- **Best Performance**: Random Forest F1-Score improved by 1.16%
- **Overall**: Slight improvement in precision-recall balance

#### 4. **AUC-ROC Stability**
- **Average AUC-ROC Change**: -0.40% across all models
- **Minimal Impact**: SMOTE doesn't significantly affect overall discriminative ability
- **Consistency**: All models maintain excellent performance (>0.96)

## Feature Importance Analysis

### SHAP Analysis Results
SMOTE-balanced models show similar feature importance patterns to original models:

#### Top 5 Features (SMOTE Models)
1. **days_since_first_infusion** - Treatment duration remains most important
2. **total_infusions** - Treatment adherence patterns
3. **financial_asst_active_flag** - Financial support impact
4. **avg_infusion_days_gap** - Treatment consistency
5. **ocr_insc_amt_covered_12_mo** - Insurance coverage

#### Feature Importance Stability
- **Core Features**: Top predictive features remain consistent
- **Ranking Stability**: Minimal changes in feature importance order
- **Clinical Validity**: SMOTE preserves meaningful clinical relationships

## Clinical and Business Implications

### Advantages of SMOTE-Balanced Models

#### 1. **Improved Minority Class Detection**
- **Higher Recall**: Better identification of patients likely to discontinue
- **Early Intervention**: More opportunities for proactive patient management
- **Reduced Attrition**: Potential to prevent more discontinuations

#### 2. **Reduced Model Bias**
- **Balanced Training**: Models see equal representation of both classes
- **Fair Predictions**: Less bias toward majority class (active patients)
- **Robust Performance**: Better generalization across patient populations

#### 3. **Enhanced Clinical Utility**
- **Sensitivity**: Better at catching high-risk patients
- **Comprehensive Coverage**: Fewer missed discontinuation cases
- **Proactive Care**: Supports preventive intervention strategies

### Disadvantages of SMOTE-Balanced Models

#### 1. **Increased False Positives**
- **Lower Precision**: More patients incorrectly flagged as high-risk
- **Resource Allocation**: Potential waste of intervention resources
- **Alert Fatigue**: Risk of overwhelming clinical staff with false alarms

#### 2. **Synthetic Data Concerns**
- **Artificial Samples**: Training includes SMOTE-generated synthetic patients
- **Data Authenticity**: Questions about representativeness of synthetic samples
- **Regulatory Considerations**: Potential concerns in regulated environments

#### 3. **Computational Overhead**
- **Larger Training Set**: 57% increase in training data size
- **Training Time**: Longer model training and validation processes
- **Storage Requirements**: Additional space needed for balanced datasets

## Recommendations

### Production Deployment Strategy

#### **Scenario 1: High-Precision Requirements**
- **Use**: Original baseline models (especially XGBoost)
- **Rationale**: Minimize false positives, focus on confident predictions
- **Best For**: Resource-constrained environments, high-cost interventions

#### **Scenario 2: High-Recall Requirements**
- **Use**: SMOTE-balanced models
- **Rationale**: Maximize detection of at-risk patients
- **Best For**: Preventive care programs, early intervention strategies

#### **Scenario 3: Balanced Performance**
- **Use**: SMOTE-balanced models with optimized thresholds
- **Rationale**: Best F1-Score performance with tunable precision-recall trade-off
- **Best For**: General clinical decision support systems

#### **Scenario 4: Hybrid Approach**
- **Use**: Ensemble of original and SMOTE models
- **Rationale**: Combine strengths of both approaches
- **Best For**: Complex clinical environments with multiple use cases

### Implementation Guidelines

#### 1. **Model Selection Criteria**
```
IF (recall_priority > precision_priority):
    USE SMOTE-balanced models
ELIF (precision_priority > recall_priority):
    USE original baseline models
ELSE:
    EVALUATE F1-scores and choose best performing approach
```

#### 2. **Threshold Optimization**
- **SMOTE Models**: Consider lowering classification threshold for higher recall
- **Original Models**: Consider raising threshold for higher precision
- **Custom Thresholds**: Optimize based on specific clinical requirements

#### 3. **Monitoring and Validation**
- **Performance Tracking**: Monitor both precision and recall in production
- **Clinical Feedback**: Validate predictions with actual patient outcomes
- **Model Drift**: Regular retraining with updated data

## Conclusion

The SMOTE experiment demonstrates that class balancing can improve recall for minority class detection at the cost of some precision. The choice between original and SMOTE-balanced models should be based on specific clinical priorities:

- **For maximum precision**: Use original XGBoost model (AUC-ROC: 0.9818, Precision: 0.9530)
- **For maximum recall**: Use SMOTE-balanced Random Forest (Recall: 0.9549, F1-Score: 0.9211)
- **For balanced performance**: Use SMOTE-balanced models with appropriate threshold tuning

Both approaches provide excellent predictive performance and can support effective clinical decision-making for Ocrevus discontinuation prediction.

---

## Technical Artifacts

- **SMOTE Script**: `baseline_ensemble_models_with_smote.py`
- **Enhanced Notebook**: `baseline_ensemble_models_analysis_updated.ipynb` (Section 11)
- **SMOTE Visualizations**: `baseline_ensemble_models_smote_evaluation.png`
- **SMOTE SHAP Analysis**: `shap_feature_importance_smote_comparison.png`
- **Feature Importance Data**: `shap_feature_importance_smote_detailed.csv`

*SMOTE Experiment completed by Augment Agent - December 2024*
